<?php

namespace App\Service;

use App\Entity\Enseigne;
use App\Entity\FormField;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints;

class DynamicFormBuilder
{
    private array $typeMapping;

    private const array CONSTRAINT_MAPPING = [
        'email' => Constraints\Email::class,
        'length' => Constraints\Length::class,
        'regex' => Constraints\Regex::class,
    ];

    public function __construct()
    {
        $this->typeMapping = [];
        foreach (FormDefinitions::getFieldDefinitions() as $type => $definition) {
            $this->typeMapping[$type] = $definition->formType;
        }
    }

    public function buildForm(FormBuilderInterface $builder, Enseigne $enseigne): void
    {
        foreach ($enseigne->getSections() as $section) {
            foreach ($section->getFields() as $field) {
                $this->addField($builder, $field);
            }
        }
    }

    private function addField(FormBuilderInterface $builder, FormField $field): void
    {
        $formType = $this->typeMapping[$field->getType()] ?? TextType::class;
        $typeDefinition = FormDefinitions::getFieldDefinition($field->getType());

        $options = array_merge([
            'label' => $field->getLabel(),
            'required' => $field->isRequired(),
            'help' => $field->getHelp(),
            'help_html' => true,
        ], $typeDefinition->formConfig ?? [], $field->getOptions());

        $builder->add($field->getName(), $formType, $options);
    }

    private function createConstraints(FormField $field): array
    {
        $constraints = [];
        if ($field->getValidationRules()) {
            $rules = json_decode($field->getValidationRules(), true);
            foreach ($rules as $rule => $params) {
                if (isset(self::CONSTRAINT_MAPPING[$rule])) {
                    $constraintClass = self::CONSTRAINT_MAPPING[$rule];
                    $constraints[] = new $constraintClass($params);
                }
            }
        }

        return $constraints;
    }
}
