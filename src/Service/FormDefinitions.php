<?php

namespace App\Service;

use App\DTO\FieldConfig;
use App\DTO\FieldDefinition;
use App\Entity\Enseigne;
use App\Form\Type\CodePharmuppType;
use App\Form\Type\FinessType;
use App\Form\Type\SiretType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CountryType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Range;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Constraints\Uuid;

class FormDefinitions
{
    public const string PHONE_MASK = '\\+\\3\\31|2|3|4|5|6|7|8|\\99{8}';
    public const string PATTERN_PHONE = '/\+33[1-9]\d{8}/';
    public const string MESSAGE_PHONE = 'Le numéro de téléphone est incorrect (format Français attendu : +33XXXXXXXXX)';

    /**
     * @param array<string> $poles
     *
     * @return array<string, FieldDefinition>
     */
    public static function getFieldDefinitions(array $poles = []): array
    {
        $fields = [
            // Formatted
            'managerEmail' => FieldDefinition::formatted(
                icon: 'fas fa-envelope',
                label: 'Adresse e-mail responsable',
                formType: EmailType::class,
                config: new FieldConfig(
                    label: 'Adresse e-mail du responsable',
                    type: 'managerEmail',
                    required: true,
                    help: 'Vous recevrez par e-mail le contrat signé à la fin de la procédure d\'adhésion'
                ),
                required: true,
            ),

            'siret' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'SIRET',
                formType: SiretType::class,
                config: new FieldConfig(
                    label: 'SIRET',
                    type: 'siret',
                    required: false,
                    help: 'Le numéro SIRET doit contenir exactement 14 chiffres'
                )
            ),

            'finess' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'FINESS',
                formType: FinessType::class,
                config: new FieldConfig(
                    label: 'FINESS',
                    type: 'finess',
                    required: false,
                    help: 'N° commençant par le numéro de votre département et suivi du chiffre 2 (ex : 332123456)'
                ),
                poles: [Enseigne::POLE_SANTE],
            ),

            'codePharmupp' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'Votre code ID PHARM-UPP',
                formType: CodePharmuppType::class,
                config: new FieldConfig(
                    label: 'Votre code ID PHARM-UPP* accessible sur la page Mon Officine',
                    type: 'codePharmupp',
                    required: false,
                    help: 'Code composé de 8 caractères commençant par F000'
                ),
                poles: [Enseigne::POLE_SANTE],
            ),

            'codeZefid' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'Code Zefid',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Code Zefid : Site + PDV',
                    type: 'codeZefid',
                    required: false,
                    help: 'Code composé de 6 caractères'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 6,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '*{6}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 6,
                            exactMessage: 'Le code Zefid doit contenir exactement 6 caractères',
                        ),
                        new Regex(
                            pattern: '/^[A-Z0-9]{6}$/',
                            message: 'Le code Zefid n\'est pas valide',
                            htmlPattern: false,
                            match: true,
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'codeSapStructure' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'Code SAP Structure',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Code SAP Structure',
                    type: 'codeSapStructure',
                    required: false
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 7,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{7}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 7,
                            exactMessage: 'Le code SAP Structure doit contenir exactement 7 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'codeSapMagasin' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'Code SAP Magasin',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Code SAP Magasin',
                    type: 'codeSapMagasin',
                    required: false
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 7,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{7}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 7,
                            exactMessage: 'Le Code SAP Magasin doit contenir exactement 7 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'codeSapGRPCOM' => FieldDefinition::formatted(
                icon: 'fas fa-barcode',
                label: 'Code SAP GRPCOM',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Code SAP GRPCOM',
                    type: 'codeSapGRPCOM',
                    required: false
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 7,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{7}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 7,
                            exactMessage: 'Le Code SAP GRPCOM doit contenir exactement 7 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'codeSapFacturation' => FieldDefinition::formatted(
                icon: 'fas fa-receipt',
                label: 'Code SAP Factu.',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Code SAP Facturation',
                    type: 'codeSapFacturation',
                    required: false
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 7,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{7}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 7,
                            exactMessage: 'Le Code SAP Facturation doit contenir exactement 7 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'numeroAssocie' => FieldDefinition::formatted(
                icon: 'fas fa-id-badge',
                label: "N° d'associé",
                formType: NumberType::class,
                config: new FieldConfig(
                    label: "Numéro d'associé",
                    type: 'numeroAssocie',
                    required: false
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 3,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{3}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 3,
                            exactMessage: 'Le Code SAP Facturation doit contenir exactement 3 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_BEAUTE],
            ),

            'numeroSite' => FieldDefinition::formatted(
                icon: 'fas fa-building',
                label: 'Numéro de site',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'N° de site',
                    type: 'numeroSite',
                    required: false,
                    help: 'Commence par FRA suivi de 3 caractères'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 6,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => 'FR\A*{3}',
                    ],
                    'constraints' => [
                        new Regex(
                            pattern: '/(FRA)[A-Z0-9]{3}/',
                            message: 'Le Code SAP Facturation doit commencer par FRA suivi de 3 caractères',
                            htmlPattern: false,
                            match: true,
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_BEAUTE],
            ),

            'tvaIntra' => FieldDefinition::formatted(
                icon: 'fas fa-percentage',
                label: 'N° de TVA',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'N° de TVA intracommunautaire',
                    type: 'tvaIntra',
                    required: false,
                    help: '2 lettres + 12 chiffres maximum'
                ),
                formConfig: [
                    'attr' => [
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => 'A{2}9{11,12}',
                    ],
                    'constraints' => [
                        new Regex(
                            pattern: '/^[A-Z]{2}[0-9]{1,12}$/',
                            message: 'Le numéro de TVA intracommunautaire doit être composé de 2 lettres suivies de 12 chiffres maximum',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_BEAUTE],
            ),

            'id' => FieldDefinition::formatted(
                icon: 'fas fa-fingerprint',
                label: 'ID',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'ID',
                    type: 'id',
                    required: false,
                    help: 'Code composé de 6 chiffres'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 6,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{6}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 6,
                            exactMessage: 'L\'ID doit contenir exactement 6 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_SANTE],
            ),

            'dateEntree' => FieldDefinition::formatted(
                icon: 'fas fa-calendar',
                label: "Date d'entrée",
                formType: DateType::class,
                config: new FieldConfig(
                    label: "Date souhaitée d'entrée dans le programme",
                    type: 'dateEntree',
                    required: false,
                    help: 'Déploiement sous 3 semaines'
                ),
                formConfig: [
                    'constraints' => [
                        new Range(
                            minMessage: 'La date sélectionnée est invalide',
                            min: 'today'
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_SANTE],
            ),

            'libellePharmacie' => FieldDefinition::formatted(
                icon: 'fas fa-prescription-bottle-alt',
                label: 'Libellé pharmacie',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Libellé de pharmacie',
                    type: 'libellePharmacie',
                    required: false,
                    help: '26 caractères maximum, espace compris'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 26,
                    ],
                    'constraints' => [
                        new Length(
                            max: 26,
                            maxMessage: 'Le libellé ne peut pas dépasser 26 caractères, espaces compris',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_SANTE],
            ),

            'numeroIvrylab' => FieldDefinition::formatted(
                icon: 'fas fa-vial',
                label: 'N° Ivrylab',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Numéro Ivrylab',
                    type: 'numeroIvrylab',
                    required: false,
                    help: '7 caractères sans espace'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 7,
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '9{7}',
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 7,
                            exactMessage: 'Le numéro Ivrylab doit contenir exactement 7 chiffres',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_SANTE],
            ),

            'guidTitulaire' => FieldDefinition::formatted(
                icon: 'fas fa-key',
                label: 'GUID Titulaire',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'GUID Titulaire',
                    type: 'guidTitulaire',
                    required: false,
                    help: 'Ex: 33d738b0-679f-48cc-aaec-29671f08c67d'
                ),
                formConfig: [
                    'constraints' => [
                        new Uuid(
                            message: 'Le format du GUID n\'est pas valide',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_SANTE],
            ),

            'guidEquipe' => FieldDefinition::formatted(
                icon: 'fas fa-key',
                label: 'GUID Equipe',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'GUID Equipe',
                    type: 'guidEquipe',
                    required: false,
                    help: 'Ex: 72b7c79a-b696-4fd5-a078-a860aa1b8f24'
                ),
                formConfig: [
                    'constraints' => [
                        new Uuid(
                            message: 'Le format du GUID n\'est pas valide',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_SANTE],
            ),

            'codeInterne' => FieldDefinition::formatted(
                icon: 'fas fa-qrcode',
                label: 'Code interne',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Code interne',
                    type: 'codeInterne',
                    required: false,
                    help: '7 caractères'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 7,
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 7,
                            exactMessage: 'Le code interne doit contenir exactement 7 caractères',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'codeFidelitePDV' => FieldDefinition::formatted(
                icon: 'fas fa-award',
                label: 'Code fidélité PDV',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Code fidélité PDV',
                    type: 'codeFidelitePDV',
                    required: false,
                    help: '3 caractères'
                ),
                formConfig: [
                    'attr' => [
                        'maxlength' => 3,
                    ],
                    'constraints' => [
                        new Length(
                            exactly: 3,
                            exactMessage: 'Le code fidélité PDV doit contenir exactement 3 caractères',
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_MAISON],
            ),

            'nbParfumeries' => FieldDefinition::formatted(
                icon: 'fas fa-spray-can',
                label: 'Nb Parfumeries',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Nombre de parfumeries et instituts',
                    type: 'nbParfumeries',
                    required: false,
                ),
                formConfig: [
                    'html5' => true,
                    'attr' => [
                        'max' => 999,
                        'min' => 1,
                    ],
                    'constraints' => [
                        new Range(
                            min: 1,
                            max: 999,
                        ),
                    ],
                ],
                poles: [Enseigne::POLE_BEAUTE],
            ),

            // Free
            'text' => FieldDefinition::free(
                icon: 'fas fa-text',
                label: 'Alphanumérique',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Alphanumérique',
                    type: 'text',
                    required: false
                )
            ),

            'numeric' => FieldDefinition::free(
                icon: 'fas fa-1',
                label: 'Numérique',
                formType: NumberType::class,
                config: new FieldConfig(
                    label: 'Numérique',
                    type: 'numeric',
                    required: false
                ),
                formConfig: [
                    'html5' => true,
                ]
            ),

            'tel' => FieldDefinition::free(
                icon: 'fas fa-phone',
                label: 'Téléphone',
                formType: TelType::class,
                config: new FieldConfig(
                    label: 'Téléphone',
                    type: 'tel',
                    required: false
                ),
                formConfig: [
                    'attr' => [
                        'placeholder' => '+33',
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => self::PHONE_MASK,
                    ],
                    'constraints' => [
                        new Regex(
                            pattern: self::PATTERN_PHONE,
                            message: self::MESSAGE_PHONE,
                            htmlPattern: false,
                            match: true,
                        ),
                    ],
                ],
            ),

            'email' => FieldDefinition::free(
                icon: 'fas fa-envelope',
                label: 'Adresse mail',
                formType: EmailType::class,
                config: new FieldConfig(
                    label: 'Adresse mail',
                    type: 'email',
                    required: false
                )
            ),

            'date' => FieldDefinition::free(
                icon: 'fas fa-clock',
                label: 'Date',
                formType: DateType::class,
                config: new FieldConfig(
                    label: 'Date',
                    type: 'date',
                    required: false
                )
            ),

            'radio' => FieldDefinition::free(
                icon: 'fas fa-circle-dot',
                label: 'Boutons radio',
                formType: ChoiceType::class,
                config: new FieldConfig(
                    label: 'Boutons radio',
                    type: 'radio',
                    required: false,
                    options: [
                        'choices' => [],
                    ]
                ),
                formConfig: [
                    'placeholder' => 'Aucun',
                    'expanded' => true,
                    'multiple' => false,
                ]
            ),

            'checkboxes' => FieldDefinition::free(
                icon: 'fas fa-check-square',
                label: 'Case à cocher multiples',
                formType: ChoiceType::class,
                config: new FieldConfig(
                    label: 'Case à cocher multiples',
                    type: 'checkboxes',
                    required: false,
                    options: [
                        'choices' => [],
                    ]
                ),
                formConfig: [
                    'expanded' => true,
                    'multiple' => true,
                ]
            ),

            'checkbox' => FieldDefinition::free(
                icon: 'fas fa-square-check',
                label: 'Case à cocher',
                formType: CheckboxType::class,
                config: new FieldConfig(
                    label: 'Case à cocher',
                    type: 'checkbox',
                    required: false,
                )
            ),

            'select' => FieldDefinition::free(
                icon: 'fas fa-list',
                label: 'Liste déroulante',
                formType: ChoiceType::class,
                config: new FieldConfig(
                    label: 'Liste déroulante',
                    type: 'select',
                    required: false,
                    options: [
                        'choices' => [],
                    ]
                ),
                formConfig: [
                    'expanded' => false,
                    'multiple' => false,
                ]
            ),

            'country' => FieldDefinition::free(
                icon: 'fas fa-flag',
                label: 'Pays',
                formType: CountryType::class,
                config: new FieldConfig(
                    label: 'Pays',
                    type: 'country',
                    required: false,
                )
            ),

            'url' => FieldDefinition::free(
                icon: 'fas fa-link',
                label: 'URL',
                formType: UrlType::class,
                config: new FieldConfig(
                    label: 'URL',
                    type: 'url',
                    required: false,
                ),
            ),

            'gpsCoordinates' => FieldDefinition::free(
                icon: 'fas fa-map-marker-alt',
                label: 'Coordonnées GPS',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Coordonnées GPS',
                    type: 'gpsCoordinates',
                    required: false,
                    help: 'Format: latitude,longitude (ex: 48.8566,2.3522 pour Paris)'
                ),
                formConfig: [
                    'attr' => [
                        'placeholder' => '48.8566,2.3522',
                        'data-controller' => 'inputmask',
                        'data-inputmask-pattern-value' => '[-]9{1,2}[.]9{1,8}[,][-]9{1,3}[.]9{1,8}',
                    ],
                    'constraints' => [
                        new Regex(
                            pattern: '/^[-]?([0-8]?[0-9]|90)(\.[0-9]{1,8})?[,][-]?((1?[0-7]?|[0-9]?)[0-9]|180)(\.[0-9]{1,8})?$/',
                            message: 'Les coordonnées GPS doivent être au format latitude,longitude (ex: 48.8566,2.3522)',
                            htmlPattern: false,
                            match: true,
                        ),
                    ],
                ],
            ),

            'city' => FieldDefinition::free(
                icon: 'fas fa-city',
                label: 'Ville',
                formType: TextType::class,
                config: new FieldConfig(
                    label: 'Ville',
                    type: 'city',
                    required: false,
                    help: 'Saisissez le nom de la ville pour obtenir des suggestions'
                ),
                formConfig: [
                    'block_prefix' => 'geo_autocomplete',
                ],
            ),

            'region' => FieldDefinition::free(
                icon: 'fas fa-map-marked-alt',
                label: 'Région',
                formType: ChoiceType::class,
                config: new FieldConfig(
                    label: 'Région',
                    type: 'region',
                    required: false,
                ),
                formConfig: [
                    'expanded' => false,
                    'multiple' => false,
                    'placeholder' => 'Sélectionnez une région',
                    'choices' => [
                        'Auvergne-Rhône-Alpes' => 'Auvergne-Rhône-Alpes',
                        'Bourgogne-Franche-Comté' => 'Bourgogne-Franche-Comté',
                        'Bretagne' => 'Bretagne',
                        'Centre-Val de Loire' => 'Centre-Val de Loire',
                        'Corse' => 'Corse',
                        'Grand Est' => 'Grand Est',
                        'Guadeloupe' => 'Guadeloupe',
                        'Guyane' => 'Guyane',
                        'Hauts-de-France' => 'Hauts-de-France',
                        'Île-de-France' => 'Île-de-France',
                        'La Réunion' => 'La Réunion',
                        'Martinique' => 'Martinique',
                        'Mayotte' => 'Mayotte',
                        'Normandie' => 'Normandie',
                        'Nouvelle-Aquitaine' => 'Nouvelle-Aquitaine',
                        'Occitanie' => 'Occitanie',
                        'Pays de la Loire' => 'Pays de la Loire',
                        "Provence-Alpes-Côte d'Azur" => "Provence-Alpes-Côte d'Azur",
                    ],
                ]
            ),
        ];

        if (empty($poles)) {
            return $fields;
        }

        return array_filter($fields, static function (FieldDefinition $field) use ($poles) {
            return !empty(array_intersect($poles, $field->poles));
        });
    }

    public static function getFieldDefinition(string $fieldType): ?FieldDefinition
    {
        return self::getFieldDefinitions()[$fieldType] ?? null;
    }

    public static function getFieldTypes(): array
    {
        return array_keys(self::getFieldDefinitions());
    }

    /**
     * @return array<FieldDefinition>
     */
    public static function getRequiredFields(array $poles = []): array
    {
        $fields = self::getFieldDefinitions($poles);

        return array_filter($fields, static function (FieldDefinition $field) {
            return $field->required;
        });
    }
}
