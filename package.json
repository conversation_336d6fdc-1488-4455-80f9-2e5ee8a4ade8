{"devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.0", "@hotwired/stimulus": "^3.0.0", "@hotwired/turbo": "^7.1.1 || ^8.0", "@pdftron/pdfjs-express-viewer": "8.4.0", "@symfony/stimulus-bridge": "^3.2.0 || ^4.0.0", "@symfony/stimulus-bundle": "file:vendor/symfony/stimulus-bundle/assets", "@symfony/ux-turbo": "file:vendor/symfony/ux-turbo/assets", "@symfony/webpack-encore": "^4.0.0", "bootstrap": "^5.2.2", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.23.0", "dotenv": "^16.0.3", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^7.0.0", "husky": "^8.0.1", "inputmask": "^5.0.7", "regenerator-runtime": "^0.13.9", "sass": "^1.56.0", "sass-loader": "^13.0.0", "signature_pad": "^4.1.4", "sortablejs": "^1.15.0", "ts-loader": "^9.0.0", "typescript": "^4.8.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-notifier": "^1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress", "prepare": "husky install"}, "dependencies": {"@playwright/test": "^1.29.2", "dragula": "^3.7.3", "esbuild-loader": "^2.21.0", "intl-tel-input": "^25.3.1", "playwright": "^1.29.2", "speed-measure-webpack-plugin": "^1.5.0"}}