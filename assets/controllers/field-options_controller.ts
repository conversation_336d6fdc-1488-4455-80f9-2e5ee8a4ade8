import { Controller } from '@hotwired/stimulus';

interface Choice {
    label: string;
    value: string;
}

interface FieldOptions {
    choices: Record<string, string>;
}

export default class extends Controller {
    static targets = ['typeSelect', 'optionsInput', 'choicesContainer', 'choicesList'];

    declare readonly typeSelectTarget: HTMLSelectElement;
    declare readonly optionsInputTarget: HTMLInputElement;
    declare readonly choicesContainerTarget: HTMLElement;
    declare readonly choicesListTarget: HTMLElement;

    connect() {
        // Initialiser les options si elles existent déjà
        const existingOptions = this.optionsInputTarget.value;
        if (existingOptions && existingOptions !== '[object Object]' && this.choicesListTarget.children.length === 0) {
            const options: FieldOptions = JSON.parse(existingOptions);
            if (options.choices) {
                Object.entries(options.choices).forEach(([label]) => {
                    this.addChoiceElement(label);
                });
            }
        }

        // Vérifier si le type actuel nécessite des choix
        this.onTypeChange();
    }

    onTypeChange() {
        const isChoiceType = this.isChoicesType(this.typeSelectTarget.value);
        this.choicesContainerTarget.classList.toggle('d-none', !isChoiceType);

        if (isChoiceType && this.choicesListTarget.children.length === 0) {
            this.addChoice();
        }

        this.updateOptionsInput();
    }

    addChoice(event?: Event) {
        if (event) {
            event.preventDefault();
        }
        this.addChoiceElement();
    }

    addChoiceElement(label: string = '') {
        const choiceDiv = document.createElement('div');
        choiceDiv.className = 'choice-item d-flex align-items-center gap-2 mb-2';
        choiceDiv.innerHTML = `
            <input type="text" class="form-control" placeholder="Libellé" value="${label}" data-action="input->field-options#updateOptionsInput">
            <button type="button" class="btn btn-danger btn-sm" data-action="click->field-options#removeChoice">
                <i class="fas fa-times"></i>
            </button>
        `;
        this.choicesListTarget.appendChild(choiceDiv);
        this.updateOptionsInput();
    }

    removeChoice(event: Event) {
        const button = event.currentTarget as HTMLButtonElement;
        button.closest('.choice-item').remove();
        this.updateOptionsInput();
    }

    updateOptionsInput() {
        const type = this.typeSelectTarget.value;
        if (!this.isChoicesType(type)) {
            this.optionsInputTarget.value = '';
            return;
        }

        const choices: Record<string, string> = {};
        this.choicesListTarget.querySelectorAll('.choice-item').forEach(item => {
            const inputs = item.querySelectorAll('input');
            const label = inputs[0].value.trim();
            if (label) {
                choices[label] = label;
            }
        });

        const options: FieldOptions = {
            choices
        };

        this.optionsInputTarget.value = JSON.stringify(options);
    }

    isChoicesType(type: string): boolean {
        return ['select', 'radio', 'checkboxes'].includes(type);
    }
}