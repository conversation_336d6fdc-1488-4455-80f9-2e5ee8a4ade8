import { Controller } from '@hotwired/stimulus';
import Inputmask from "inputmask";

export default class extends Controller {

	getElement(id: string) {
		return <HTMLInputElement>document.getElementById(`adhesion_sepa_${id}`);
	}

	connect() {
		// Regex pour un IBAN français, à modifier si on élargie la liste des pays
		Inputmask({ regex: "FR\\d{2} \\d{4} \\d{4} \\d{2}[\\dA-Z]{2} [\\dA-Z]{4} [\\dA-Z]{4} [\\dA-Z]{1}\\d{2}" }).mask(this.getElement("iban"));
	}

}