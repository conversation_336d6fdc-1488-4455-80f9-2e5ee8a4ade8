{% extends 'admin/enseigne/base.html.twig' %}

{% import _self as formMacros %}

{% block sidebar %}
    <div class="fields-sidebar me-3">
        <div class="fs-5 fw-bold mb-3">Liste des champs</div>
        <h6 class="sidebar-title mb-3">Champs formatés</h6>
        <div class="fields-list formatted">
            {{ _self.sidebar_field_list(availableFields|filter(field => field.group == 'formatted')) }}
        </div>

        <h6 class="sidebar-title mb-3 mt-4">Champs libres</h6>
        <div class="fields-list free">
            {{ _self.sidebar_field_list(availableFields|filter(field => field.group == 'free')) }}

        </div>
    </div>
{% endblock %}

{% macro sidebar_field_list(fields) %}
    {% for field in fields %}
        {{ _self.sidebar_field_item(field) }}
    {% endfor %}
{% endmacro %}

{% macro sidebar_field_item(field) %}
    <div class="field-item d-flex align-items-center justify-content-start position-relative" draggable="true" data-field-type="{{ field.group }}" data-field-required="{{ field.required|json_encode|e('html_attr') }}" data-field-config='{{ field.config|json_encode|e('html_attr') }}' data-field-all-config='{{ field|json_encode|e('html_attr') }}'>
        <i class="{{ field.icon }} me-2"></i>
        <span class="text-nowrap text-truncate">{{ field.label }}</span>
        <div class="flex-grow-1 d-flex align-items-center justify-content-end handle">
            <i class="fas fa-grip-vertical ms-2"></i>
        </div>
        {% if field.required %}
            <div class="required-field position-absolute text-danger" style="top: -4px; right: -3px; font-size: 10px">
                <i class="fa solid fa-circle"></i>
            </div>
        {% endif %}
    </div>
{% endmacro %}

{% block form %}
    <div class="substep-title fs-5 fw-bold mb-3">Formulaire</div>

    {{ form_start(form) }}

    <div>
        {{ form_errors(form) }}
    </div>

    <div {{ stimulus_controller('form-collection', {
        value: form.sections|length > 0 ? form.sections|last.vars.name + 1 : 0,
        prototype: formMacros.section_item(form.sections.vars.prototype)|json_encode,
    }) }}>
        <div id="sections-list" data-form-collection-target="fields">
            {% do form.sections.setRendered %}
            {% for section in form.sections %}
                {{ formMacros.section_item(section) }}
            {% endfor %}
        </div>
        <button class="btn btn-primary" type="button"
                data-action="form-collection#addItem"
                data-form-collection-target="addButton">
            Ajouter une catégorie
        </button>
    </div>

    <div class="d-flex justify-content-between mt-4">
        <a href="{{ path('app_admin_enseigne_1_facturation', {id: enseigne.id}) }}" class="btn btn-secondary">Précédent</a>
        <button type="submit" class="btn btn-primary">Suivant</button>
    </div>

    {{ form_end(form) }}
{% endblock %}

{% macro section_item(form) %}
    <div class="ps-3 mb-3 border-start border-info border-3 d-flex" data-form-collection-target="field">
        <div role="button" class="handle">
            <i class="fa fa-grip-dots fa-handle fs-3 me-3"></i>
        </div>
        <div class="flex-grow-1">
            {{ form_row(form.title) }}
            {{ form_row(form.description) }}
            {{ form_row(form.position) }}

            <div {{ stimulus_controller('form-collection', {
                value: form.fields|length > 0 ? form.fields|last.vars.name + 1 : 0,
                prototype: _self.field_item(form.fields.vars.prototype)|json_encode,
                placeholder: '__field__',
            }) }}>
                <div class="fields-list" data-form-collection-target="fields">
                    {%- do form.fields.setRendered -%}
                    {%- for field in form.fields -%}
                        {{ _self.field_item(field) }}
                    {%- endfor -%}
                </div>
            </div>

            <button class="btn btn-danger mt-3" type="button" data-action="form-collection#removeItem">
                Supprimer la catégorie
            </button>
        </div>
    </div>
{% endmacro %}

{% macro field_item(form) %}
    <div class="field-container px-3 mb-3"
         data-form-collection-target="field"
         data-index="__field__"
         {{ stimulus_controller('field-options') }}>
        <div class="field-header d-flex justify-content-between align-items-center p-2">
            <div class="d-flex align-items-center">
                <span class="field-title fw-bold">
                    <i class="me-2"></i>
                    <span>
                        {% if form.vars.value %}
                            {{ form.vars.value.type }} - {{ form.vars.value.label }}
                        {% endif %}
                    </span>
                </span>
            </div>
            <div class="d-flex">
                <button class="btn btn-link text-danger p-0 me-2" type="button" data-action="form-collection#removeItem">
                    <i class="fas fa-trash"></i>
                </button>
                <div role="button" class="handle btn btn-link text-dark p-0 ">
                    <i class="fas fa-grip-vertical"></i>
                </div>
            </div>
        </div>
        <div class="field-content p-3">
            {{ form_row(form.label) }}
            {{ form_row(form.type, {
                attr: {
                    'data-action': 'change->field-options#onTypeChange',
                    'data-field-options-target': 'typeSelect',
                },
            }) }}
            {{ form_row(form.help) }}
            {{ form_row(form.required) }}
            {{ form_row(form.position) }}
            {{ form_widget(form.options, {
                attr: {
                    'data-field-options-target': 'optionsInput',
                },
            }) }}

            <div data-field-options-target="choicesContainer" class="d-none mt-3">
                <label class="form-label">Liste des choix :</label>
                <div class="choices-list" data-field-options-target="choicesList">
                    <!-- Les options seront ajoutées ici dynamiquement -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm mt-2"
                        data-action="field-options#addChoice">
                    Ajouter une option
                </button>
            </div>

            {{ form_label(form.pdfDisplayConfig) }}

            <div class="row row-cols-lg-auto g-3 align-items-center">
                <div class="col-12">
                    {{ form_row(form.pdfDisplayConfig.summary) }}
                </div>
                {% if form.pdfDisplayConfig.sepa is defined %}
                    <div class="col-12">
                        {{ form_row(form.pdfDisplayConfig.sepa) }}
                    </div>
                {% endif %}
                <div class="col-12">
                    {{ form_row(form.pdfDisplayConfig.signature) }}
                </div>
            </div>
        </div>
    </div>
{% endmacro %}

{% block scripts %}
    <div {{ stimulus_controller('enseigne-form') }}/>
    <div {{ stimulus_controller('form-errors') }}/>
{% endblock %}
